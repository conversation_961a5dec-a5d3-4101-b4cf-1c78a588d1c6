package tz.co.mikesanga.approvalflow.uaa.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tz.co.mikesanga.approvalflow.global.dtos.GqlResponseDto;
import tz.co.mikesanga.approvalflow.global.dtos.ResponseCode;
import tz.co.mikesanga.approvalflow.uaa.dtos.*;
import tz.co.mikesanga.approvalflow.uaa.entities.Role;
import tz.co.mikesanga.approvalflow.uaa.entities.UserAccount;
import tz.co.mikesanga.approvalflow.uaa.repositories.RoleRepository;
import tz.co.mikesanga.approvalflow.uaa.repositories.UserAccountRepository;
import tz.co.mikesanga.approvalflow.utils.Constants;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserManagementServiceImpl implements UserManagementService {

    private final UserAccountRepository userAccountRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;

    // Default password for new users created by admin
    private static final String DEFAULT_PASSWORD = "ChangeMe123!";

    @Override
    public GqlResponseDto<UserResponseDto> getAllUsers(UserFilterDto filterDto) {
        try {
            log.info("Fetching users with filters: {}", filterDto);

            Sort sort = Sort.by(Sort.Direction.fromString(filterDto.getSortDirection()), filterDto.getSortBy());
            Pageable pageable = PageRequest.of(filterDto.getPage(), filterDto.getSize(), sort);

            Page<UserAccount> userPage;

            if (filterDto.getRoleUuid() != null && !filterDto.getRoleUuid().isEmpty()) {
                UUID roleUuid = UUID.fromString(filterDto.getRoleUuid());
                userPage = userAccountRepository.findUsersWithFiltersAndRole(
                    filterDto.getSearch(), filterDto.getStatus(), roleUuid, pageable);
            } else {
                userPage = userAccountRepository.findUsersWithFilters(
                    filterDto.getSearch(), filterDto.getStatus(), pageable);
            }

            List<UserResponseDto> userDtos = userPage.getContent().stream()
                .map(this::convertToUserResponseDto)
                .collect(Collectors.toList());

            Map<String, Object> extras = new HashMap<>();
            extras.put("totalElements", userPage.getTotalElements());
            extras.put("totalPages", userPage.getTotalPages());
            extras.put("currentPage", userPage.getNumber());
            extras.put("pageSize", userPage.getSize());
            extras.put("hasNext", userPage.hasNext());
            extras.put("hasPrevious", userPage.hasPrevious());

            return new GqlResponseDto<>(true, ResponseCode.SUCCESS, userDtos, extras);

        } catch (Exception e) {
            log.error("Error fetching users: ", e);
            return new GqlResponseDto<>(false, ResponseCode.FAILURE, null, "Error fetching users: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public GqlResponseDto<UserResponseDto> saveUser(UserCreateRequestDto userDto) {
        try {
            log.info("Saving user with username: {}", userDto.getUsername());

            // Check if this is an update operation (UUID provided)
            Optional<UserAccount> optionalUser = getOptionalByUuid(userDto.getUuid());
            if (userDto.getUuid() != null && optionalUser.isEmpty()) {
                return new GqlResponseDto<>(false, ResponseCode.CUSTOM, null, "User edit failed: The user was not found or may have been removed.");
            }

            UserAccount userAccount = optionalUser.orElse(new UserAccount());

            // For update operations, validate uniqueness excluding current user
            if (userDto.getUuid() != null) {
                log.info("Updating existing user with UUID: {}", userDto.getUuid());

                // Validate username uniqueness (excluding current user)
                if (userAccountRepository.existsByUsernameAndUuidNot(userDto.getUsername(), userAccount.getUuid())) {
                    return new GqlResponseDto<>(false, ResponseCode.DUPLICATE, null, "Username already exists");
                }

                // Validate email uniqueness (excluding current user)
                if (userAccountRepository.existsByEmailAndUuidNot(userDto.getEmail(), userAccount.getUuid())) {
                    return new GqlResponseDto<>(false, ResponseCode.DUPLICATE, null, "Email already exists");
                }
                userAccount.setUsername(userDto.getUsername());
                userAccount.setUpdatedAt(LocalDateTime.now());
            } else {
                // This is a create operation
                log.info("Creating new user with username: {}", userDto.getUsername());

                // Validate username uniqueness
                if (userAccountRepository.existsByUsername(userDto.getUsername())) {
                    return new GqlResponseDto<>(false, ResponseCode.DUPLICATE, null, "Username already exists");
                }

                // Validate email uniqueness
                if (userAccountRepository.existsByEmail(userDto.getEmail())) {
                    return new GqlResponseDto<>(false, ResponseCode.DUPLICATE, null, "Email already exists");
                }

                // Set initial values for new user
                userAccount.setUsername(userDto.getUsername());
                log.error("here is the password you are looking for: {}", userDto.getPassword());
                // Use provided password or default password
                String passwordToUse = (userDto.getPassword() != null && !userDto.getPassword().trim().isEmpty())
                    ? userDto.getPassword()
                    : DEFAULT_PASSWORD;
                userAccount.setPassword(passwordEncoder.encode(passwordToUse));
                userAccount.setStatus(UserAccount.UserStatus.ACTIVE);

                log.info("New user will be created with {} password",
                    (userDto.getPassword() != null && !userDto.getPassword().trim().isEmpty()) ? "provided" : "default");
            }

            // Set common fields
            userAccount.setFullName(userDto.getFullName());
            userAccount.setEmail(userDto.getEmail());
            userAccount.setPhoneCode(userDto.getPhoneCode());
            userAccount.setPhone(userDto.getPhone());

            // Update password if provided (for updates)
            if (userDto.getUuid() != null && userDto.getPassword() != null && !userDto.getPassword().trim().isEmpty()) {
                userAccount.setPassword(passwordEncoder.encode(userDto.getPassword()));
                log.info("Password updated for user: {}", userDto.getUsername());
            }

            // Set status if provided (for updates)
            if (userDto.getStatus() != null) {
                userAccount.setStatus(userDto.getStatus());
            }

            // Handle roles assignment
            if (userDto.getRoleUuids() != null && !userDto.getRoleUuids().isEmpty()) {
                Optional<Role> systemUserRole = roleRepository.findFirstByName(Constants.SystemUserRole.NAME);
                if (systemUserRole.isPresent()) {
                    userAccount.setRoles(List.of(systemUserRole.get()));
                    log.info("Automatically assigned SYSTEM_USER role to new user: {}", userDto.getUsername());
                } else {
                    log.warn("SYSTEM_USER role not found, user created without default role");
                }

                List<Role> roles = new ArrayList<>();
                for (String roleUuid : userDto.getRoleUuids()) {
                    Optional<Role> roleOpt = roleRepository.findFirstByUuid(UUID.fromString(roleUuid));
                    if (roleOpt.isPresent()) {
                        roles.add(roleOpt.get());
                    } else {
                        log.warn("Role with UUID {} not found", roleUuid);
                    }
                }
                userAccount.setRoles(roles);
            }
            UserAccount savedUser = userAccountRepository.save(userAccount);
            UserResponseDto responseDto = convertToUserResponseDto(savedUser);

            log.info("User saved successfully with ID: {}", savedUser.getId());
            return new GqlResponseDto<>(true, ResponseCode.SUCCESS, responseDto);

        } catch (Exception e) {
            log.error("Error saving user: ", e);
            return new GqlResponseDto<>(false, ResponseCode.FAILURE, null, "Error saving user: " + e.getMessage());
        }
    }



    @Override
    public GqlResponseDto<UserResponseDto> getUserByUuid(String uuid) {
        try {
            log.info("Fetching user with UUID: {}", uuid);

            Optional<UserAccount> userOpt = userAccountRepository.findFirstByUuid(UUID.fromString(uuid));
            if (userOpt.isEmpty()) {
                return new GqlResponseDto<>(false, ResponseCode.NO_RECORD_FOUND, null, "User not found");
            }

            UserResponseDto responseDto = convertToUserResponseDto(userOpt.get());
            return new GqlResponseDto<>(true, ResponseCode.SUCCESS, responseDto);

        } catch (Exception e) {
            log.error("Error fetching user: ", e);
            return new GqlResponseDto<>(false, ResponseCode.FAILURE, null, "Error fetching user: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public GqlResponseDto<UserResponseDto> deactivateUser(String uuid) {
        try {
            log.info("Deactivating user with UUID: {}", uuid);

            Optional<UserAccount> userOpt = userAccountRepository.findFirstByUuid(UUID.fromString(uuid));
            if (userOpt.isEmpty()) {
                return new GqlResponseDto<>(false, ResponseCode.NO_RECORD_FOUND, null, "User not found");
            }

            UserAccount userAccount = userOpt.get();
            userAccount.setStatus(UserAccount.UserStatus.INACTIVE);
            userAccount.setUpdatedAt(LocalDateTime.now());

            UserAccount savedUser = userAccountRepository.save(userAccount);
            UserResponseDto responseDto = convertToUserResponseDto(savedUser);

            log.info("User deactivated successfully with ID: {}", savedUser.getId());
            return new GqlResponseDto<>(true, ResponseCode.SUCCESS, responseDto);

        } catch (Exception e) {
            log.error("Error deactivating user: ", e);
            return new GqlResponseDto<>(false, ResponseCode.FAILURE, null, "Error deactivating user: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public GqlResponseDto<UserResponseDto> reactivateUser(String uuid) {
        try {
            log.info("Reactivating user with UUID: {}", uuid);

            Optional<UserAccount> userOpt = userAccountRepository.findFirstByUuid(UUID.fromString(uuid));
            if (userOpt.isEmpty()) {
                return new GqlResponseDto<>(false, ResponseCode.NO_RECORD_FOUND, null, "User not found");
            }

            UserAccount userAccount = userOpt.get();
            userAccount.setStatus(UserAccount.UserStatus.ACTIVE);
            userAccount.setUpdatedAt(LocalDateTime.now());

            UserAccount savedUser = userAccountRepository.save(userAccount);
            UserResponseDto responseDto = convertToUserResponseDto(savedUser);

            log.info("User reactivated successfully with ID: {}", savedUser.getId());
            return new GqlResponseDto<>(true, ResponseCode.SUCCESS, responseDto);

        } catch (Exception e) {
            log.error("Error reactivating user: ", e);
            return new GqlResponseDto<>(false, ResponseCode.FAILURE, null, "Error reactivating user: " + e.getMessage());
        }
    }



    @Override
    @Transactional
    public GqlResponseDto<UserAccount> changeUserStatus(String uuid, UserAccount.UserStatus status) {
        try {
            log.info("Changing user status to {} for UUID: {}", status, uuid);

            Optional<UserAccount> userOpt = userAccountRepository.findFirstByUuid(UUID.fromString(uuid));
            if (userOpt.isEmpty()) {
                return new GqlResponseDto<>(false, ResponseCode.NO_RECORD_FOUND, null, "User not found");
            }

            UserAccount userAccount = userOpt.get();
            userAccount.setStatus(status);
            userAccount.setUpdatedAt(LocalDateTime.now());

            UserAccount savedUser = userAccountRepository.save(userAccount);

            log.info("User status changed successfully for ID: {}", savedUser.getId());
            return new GqlResponseDto<>(true, ResponseCode.SUCCESS, savedUser);

        } catch (Exception e) {
            log.error("Error changing user status: ", e);
            return new GqlResponseDto<>(false, ResponseCode.FAILURE, null, "Error changing user status: " + e.getMessage());
        }
    }

    private UserResponseDto convertToUserResponseDto(UserAccount userAccount) {
        UserResponseDto dto = new UserResponseDto();
        dto.setUuid(userAccount.getUuid().toString());
        dto.setUsername(userAccount.getUsername());
        dto.setFullName(userAccount.getFullName());
        dto.setEmail(userAccount.getEmail());
        dto.setPhoneCode(userAccount.getPhoneCode());
        dto.setPhone(userAccount.getPhone());
        dto.setStatus(userAccount.getStatus());
        dto.setLastLogin(userAccount.getLastLogin());
        dto.setCreatedAt(userAccount.getCreatedAt());
        dto.setUpdatedAt(userAccount.getUpdatedAt());

        // Convert roles
        if (userAccount.getRoles() != null) {
            List<UserResponseDto.RoleResponseDto> roleDtos = userAccount.getRoles().stream()
                .map(role -> new UserResponseDto.RoleResponseDto(
                    role.getUuid().toString(),
                    role.getName(),
                    role.getDisplayName(),
                    role.getDescription()
                ))
                .collect(Collectors.toList());
            dto.setRoles(roleDtos);
        }

        return dto;
    }

    /**
     * Helper method to get UserAccount by UUID (following RoleServiceImpl pattern)
     */
    private Optional<UserAccount> getOptionalByUuid(String uuid) {
        return uuid != null && !uuid.isEmpty() ? userAccountRepository.findFirstByUuid(UUID.fromString(uuid)) : Optional.empty();
    }
}
