package tz.co.mikesanga.approvalflow;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.data.jpa.datatables.repository.DataTablesRepositoryFactoryBean;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import tz.co.mikesanga.approvalflow.llm.config.OllamaApiProperties;

@SpringBootApplication
@EnableJpaRepositories(repositoryFactoryBeanClass = DataTablesRepositoryFactoryBean.class)
@EnableConfigurationProperties(OllamaApiProperties.class)
public class ApprovalFlowApplication {

    public static void main(String[] args) {
        SpringApplication.run(ApprovalFlowApplication.class, args);
    }

}
