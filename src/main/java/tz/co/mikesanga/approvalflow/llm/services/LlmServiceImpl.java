package tz.co.mikesanga.approvalflow.llm.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tz.co.mikesanga.approvalflow.global.dtos.GqlResponseDto;
import tz.co.mikesanga.approvalflow.global.dtos.ResponseCode;
import tz.co.mikesanga.approvalflow.llm.clients.OllamaApiClient;
import tz.co.mikesanga.approvalflow.llm.config.OllamaApiProperties;
import tz.co.mikesanga.approvalflow.llm.dtos.*;
import tz.co.mikesanga.approvalflow.llm.exceptions.OllamaApiException;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service implementation for LLM operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class LlmServiceImpl implements LlmService {

    private final OllamaApiClient ollamaApiClient;
    private final OllamaApiProperties ollamaApiProperties;

    @Override
    public GqlResponseDto<LlmChatResponse> chat(LlmChatRequest request) {
        log.info("Processing LLM chat request with message: {}", request.getMessage());

        if (!ollamaApiProperties.isEnabled()) {
            log.warn("Ollama API is disabled");
            return new GqlResponseDto<>(false, ResponseCode.FAILURE, null, "LLM service is currently disabled");
        }

        try {
            // Build Ollama request
            OllamaChatRequest ollamaRequest = buildOllamaRequest(request);
            
            // Record start time for performance metrics
            long startTime = System.currentTimeMillis();
            
            // Call Ollama API
            OllamaChatResponse ollamaResponse = ollamaApiClient.chatCompletion(ollamaRequest);
            
            // Calculate response time
            long responseTime = System.currentTimeMillis() - startTime;
            
            // Convert to our response format
            LlmChatResponse response = convertToLlmResponse(ollamaResponse, responseTime);
            
            log.info("LLM chat completed successfully in {}ms", responseTime);
            return new GqlResponseDto<>(true, ResponseCode.SUCCESS, response);
            
        } catch (OllamaApiException e) {
            log.error("Ollama API error: {}", e.getMessage(), e);
            return new GqlResponseDto<>(false, ResponseCode.FAILURE, null,
                    "LLM service error: " + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error during LLM chat", e);
            return new GqlResponseDto<>(false, ResponseCode.FAILURE, null,
                    "An unexpected error occurred while processing your request");
        }
    }

    @Override
    public GqlResponseDto<LlmChatResponse> testConnection() {
        log.info("Testing LLM connection");
        
        LlmChatRequest testRequest = new LlmChatRequest();
        testRequest.setMessage("Hello! Please respond with a simple greeting to confirm the connection is working.");
        testRequest.setModel(ollamaApiProperties.getDefaultModel());
        testRequest.setTemperature(0.7);
        testRequest.setMaxTokens(50);
        
        return chat(testRequest);
    }

    @Override
    public boolean isServiceAvailable() {
        if (!ollamaApiProperties.isEnabled()) {
            return false;
        }
        
        try {
            // Try a simple test request
            GqlResponseDto<LlmChatResponse> response = testConnection();
            return response.getStatus();
        } catch (Exception e) {
            log.warn("LLM service availability check failed", e);
            return false;
        }
    }

    /**
     * Build Ollama API request from our request format
     */
    private OllamaChatRequest buildOllamaRequest(LlmChatRequest request) {
        OllamaChatRequest ollamaRequest = new OllamaChatRequest();
        
        // Set model (use provided model or default)
        String model = request.getModel() != null ? request.getModel() : ollamaApiProperties.getDefaultModel();
        ollamaRequest.setModel(model);
        
        // Create message list
        List<OllamaChatMessage> messages = List.of(
                OllamaChatMessage.userMessage(request.getMessage())
        );
        ollamaRequest.setMessages(messages);
        
        // Set streaming to false for simplicity
        ollamaRequest.setStream(false);
        
        // Set options if provided
        if (request.getTemperature() != null || request.getMaxTokens() != null) {
            OllamaChatOptions options = new OllamaChatOptions();
            options.setTemperature(request.getTemperature());
            options.setNumPredict(request.getMaxTokens());
            ollamaRequest.setOptions(options);
        }
        
        return ollamaRequest;
    }

    /**
     * Convert Ollama response to our response format
     */
    private LlmChatResponse convertToLlmResponse(OllamaChatResponse ollamaResponse, long responseTime) {
        LlmChatResponse response = new LlmChatResponse();
        
        response.setMessage(ollamaResponse.getMessage() != null ? ollamaResponse.getMessage().getContent() : "");
        response.setModel(ollamaResponse.getModel());
        response.setTimestamp(LocalDateTime.now());
        response.setResponseTimeMs(responseTime);
        response.setSuccess(true);
        
        // Set token counts if available
        if (ollamaResponse.getPromptEvalCount() != null) {
            response.setPromptTokens(ollamaResponse.getPromptEvalCount());
        }
        if (ollamaResponse.getEvalCount() != null) {
            response.setResponseTokens(ollamaResponse.getEvalCount());
        }
        if (response.getPromptTokens() != null && response.getResponseTokens() != null) {
            response.setTotalTokens(response.getPromptTokens() + response.getResponseTokens());
        }
        
        return response;
    }
}
