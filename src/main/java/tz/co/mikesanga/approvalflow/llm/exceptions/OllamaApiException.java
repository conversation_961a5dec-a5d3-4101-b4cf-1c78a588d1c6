package tz.co.mikesanga.approvalflow.llm.exceptions;

/**
 * Exception thrown when Ollama API calls fail
 */
public class OllamaApiException extends RuntimeException {

    private final int statusCode;

    public OllamaApiException(String message, int statusCode) {
        super(message);
        this.statusCode = statusCode;
    }

    public OllamaApiException(String message, Throwable cause, int statusCode) {
        super(message, cause);
        this.statusCode = statusCode;
    }

    public int getStatusCode() {
        return statusCode;
    }
}
