package tz.co.mikesanga.approvalflow.llm.dtos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for Ollama chat completion options
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OllamaChatOptions {

    /**
     * Temperature for response generation (0.0 to 1.0)
     */
    @JsonProperty("temperature")
    private Double temperature;

    /**
     * Maximum number of tokens to generate
     */
    @JsonProperty("num_predict")
    private Integer numPredict;

    /**
     * Top-k sampling parameter
     */
    @JsonProperty("top_k")
    private Integer topK;

    /**
     * Top-p sampling parameter
     */
    @JsonProperty("top_p")
    private Double topP;

    /**
     * Seed for reproducible outputs
     */
    @JsonProperty("seed")
    private Integer seed;

    /**
     * Repeat penalty
     */
    @JsonProperty("repeat_penalty")
    private Double repeatPenalty;
}
