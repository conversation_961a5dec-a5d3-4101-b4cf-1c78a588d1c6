package tz.co.mikesanga.approvalflow.llm.dtos;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO representing a message in Ollama chat conversation
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OllamaChatMessage {

    /**
     * Role of the message sender (system, user, assistant, tool)
     */
    @NotBlank(message = "Message role is required")
    @JsonProperty("role")
    private String role;

    /**
     * Content of the message
     */
    @NotBlank(message = "Message content is required")
    @JsonProperty("content")
    private String content;

    /**
     * Convenience constructor for user messages
     */
    public static OllamaChatMessage userMessage(String content) {
        return new OllamaChatMessage("user", content);
    }

    /**
     * Convenience constructor for system messages
     */
    public static OllamaChatMessage systemMessage(String content) {
        return new OllamaChatMessage("system", content);
    }

    /**
     * Convenience constructor for assistant messages
     */
    public static OllamaChatMessage assistantMessage(String content) {
        return new OllamaChatMessage("assistant", content);
    }
}
