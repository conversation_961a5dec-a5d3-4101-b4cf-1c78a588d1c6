package tz.co.mikesanga.approvalflow.llm.controllers;

import io.leangen.graphql.annotations.GraphQLArgument;
import io.leangen.graphql.annotations.GraphQLMutation;
import io.leangen.graphql.annotations.GraphQLQuery;
import io.leangen.graphql.spqr.spring.annotations.GraphQLApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import tz.co.mikesanga.approvalflow.global.dtos.GqlResponseDto;
import tz.co.mikesanga.approvalflow.llm.dtos.LlmChatRequest;
import tz.co.mikesanga.approvalflow.llm.dtos.LlmChatResponse;
import tz.co.mikesanga.approvalflow.llm.services.LlmService;

/**
 * GraphQL controller for LLM operations
 */
@GraphQLApi
@Service
@RequiredArgsConstructor
@Slf4j
public class LlmController {

    private final LlmService llmService;

    /**
     * Test LLM connection with a simple "Hello World" message
     *
     * @return the test response
     */
    @GraphQLQuery(name = "testLlmConnection", description = "Test LLM connection with a Hello World message")
    @PreAuthorize("hasRole('ROLE_LLM_TEST')")
    public GqlResponseDto<LlmChatResponse> testLlmConnection() {
        log.info("Testing LLM connection via GraphQL");
        return llmService.testConnection();
    }

    /**
     * Send a chat message to the LLM
     *
     * @param request the chat request
     * @return the chat response
     */
    @GraphQLMutation(name = "sendLlmMessage", description = "Send a message to the LLM and get a response")
    @PreAuthorize("hasRole('ROLE_LLM_CHAT')")
    public GqlResponseDto<LlmChatResponse> sendLlmMessage(
            @GraphQLArgument(name = "request") LlmChatRequest request) {
        log.info("Processing LLM chat request via GraphQL for message: {}", request.getMessage());
        return llmService.chat(request);
    }

    /**
     * Check if the LLM service is available
     *
     * @return service availability status
     */
    @GraphQLQuery(name = "isLlmServiceAvailable", description = "Check if the LLM service is available")
    @PreAuthorize("hasRole('ROLE_LLM_VIEW')")
    public boolean isLlmServiceAvailable() {
        log.info("Checking LLM service availability via GraphQL");
        return llmService.isServiceAvailable();
    }

    /**
     * Simple test endpoint that doesn't require Ollama to be running
     *
     * @return a simple test message
     */
    @GraphQLQuery(name = "testLlmController", description = "Test the LLM controller without calling external services")
    @PreAuthorize("hasRole('ROLE_LLM_VIEW')")
    public String testLlmController() {
        log.info("Testing LLM controller endpoint");
        return "LLM Controller is working! This endpoint confirms the GraphQL integration is functional.";
    }
}
