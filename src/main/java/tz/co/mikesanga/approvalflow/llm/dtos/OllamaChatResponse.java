package tz.co.mikesanga.approvalflow.llm.dtos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * DTO for Ollama chat completion response
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OllamaChatResponse {

    /**
     * Model used for the response
     */
    @JsonProperty("model")
    private String model;

    /**
     * Timestamp when the response was created
     */
    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    /**
     * The response message
     */
    @JsonProperty("message")
    private OllamaChatMessage message;

    /**
     * Whether the response is complete
     */
    @JsonProperty("done")
    private boolean done;

    /**
     * Total duration in nanoseconds
     */
    @JsonProperty("total_duration")
    private Long totalDuration;

    /**
     * Load duration in nanoseconds
     */
    @JsonProperty("load_duration")
    private Long loadDuration;

    /**
     * Prompt evaluation count
     */
    @JsonProperty("prompt_eval_count")
    private Integer promptEvalCount;

    /**
     * Prompt evaluation duration in nanoseconds
     */
    @JsonProperty("prompt_eval_duration")
    private Long promptEvalDuration;

    /**
     * Evaluation count
     */
    @JsonProperty("eval_count")
    private Integer evalCount;

    /**
     * Evaluation duration in nanoseconds
     */
    @JsonProperty("eval_duration")
    private Long evalDuration;

    /**
     * Reason why the response is done (optional)
     */
    @JsonProperty("done_reason")
    private String doneReason;
}
