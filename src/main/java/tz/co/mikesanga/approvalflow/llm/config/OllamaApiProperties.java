package tz.co.mikesanga.approvalflow.llm.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Configuration properties for Ollama API integration
 */
@Component
@ConfigurationProperties(prefix = "ollama.api")
@Getter
@Setter
public class OllamaApiProperties {

    /**
     * Base URL for Ollama API
     */
    private String baseUrl = "http://localhost:11434";

    /**
     * Connection timeout in milliseconds
     */
    private int connectTimeout = 10000;

    /**
     * Read timeout in milliseconds
     */
    private int readTimeout = 60000;

    /**
     * Default model to use for LLM operations
     */
    private String defaultModel = "llama3.2";

    /**
     * Whether Ollama API integration is enabled
     */
    private boolean enabled = true;
}
