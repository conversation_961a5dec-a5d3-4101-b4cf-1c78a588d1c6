package tz.co.mikesanga.approvalflow.llm.services;

import tz.co.mikesanga.approvalflow.global.dtos.GqlResponseDto;
import tz.co.mikesanga.approvalflow.llm.dtos.LlmChatRequest;
import tz.co.mikesanga.approvalflow.llm.dtos.LlmChatResponse;

/**
 * Service interface for LLM operations
 */
public interface LlmService {

    /**
     * Send a chat message to the LLM and get a response
     *
     * @param request the chat request
     * @return the chat response wrapped in GqlResponseDto
     */
    GqlResponseDto<LlmChatResponse> chat(LlmChatRequest request);

    /**
     * Test the LLM connection with a simple "Hello World" message
     *
     * @return the test response wrapped in GqlResponseDto
     */
    GqlResponseDto<LlmChatResponse> testConnection();

    /**
     * Check if the LLM service is available
     *
     * @return true if the service is available, false otherwise
     */
    boolean isServiceAvailable();
}
