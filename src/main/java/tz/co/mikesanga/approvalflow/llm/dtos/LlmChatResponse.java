package tz.co.mikesanga.approvalflow.llm.dtos;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * DTO for LLM chat response to GraphQL API
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class LlmChatResponse {

    /**
     * The response message from the LLM
     */
    private String message;

    /**
     * Model used for the response
     */
    private String model;

    /**
     * Timestamp when the response was generated
     */
    private LocalDateTime timestamp;

    /**
     * Response time in milliseconds
     */
    private Long responseTimeMs;

    /**
     * Number of tokens in the prompt
     */
    private Integer promptTokens;

    /**
     * Number of tokens in the response
     */
    private Integer responseTokens;

    /**
     * Total number of tokens used
     */
    private Integer totalTokens;

    /**
     * Whether the response was successful
     */
    private boolean success;

    /**
     * Error message if the response was not successful
     */
    private String errorMessage;
}
