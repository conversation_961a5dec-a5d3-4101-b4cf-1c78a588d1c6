package tz.co.mikesanga.approvalflow.llm.dtos;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * DTO for Ollama chat completion request
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OllamaChatRequest {

    /**
     * Model name to use for the chat completion
     */
    @NotBlank(message = "Model name is required")
    @JsonProperty("model")
    private String model;

    /**
     * List of messages in the conversation
     */
    @NotEmpty(message = "Messages list cannot be empty")
    @Valid
    @JsonProperty("messages")
    private List<OllamaChatMessage> messages;

    /**
     * Whether to stream the response (default: false for simplicity)
     */
    @JsonProperty("stream")
    private boolean stream = false;

    /**
     * Additional options for the model (optional)
     */
    @JsonProperty("options")
    private OllamaChatOptions options;

    /**
     * How long to keep the model loaded (optional)
     */
    @JsonProperty("keep_alive")
    private String keepAlive;
}
