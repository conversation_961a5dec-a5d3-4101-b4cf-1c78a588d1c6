package tz.co.mikesanga.approvalflow.llm.clients;

import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tz.co.mikesanga.approvalflow.llm.exceptions.OllamaApiException;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * Custom error decoder for Ollama API Feign client
 */
@Component
@Slf4j
public class OllamaErrorDecoder implements ErrorDecoder {

    @Override
    public Exception decode(String methodKey, Response response) {
        String errorMessage = "Unknown error occurred";
        
        try {
            if (response.body() != null) {
                errorMessage = new String(response.body().asInputStream().readAllBytes(), StandardCharsets.UTF_8);
            }
        } catch (IOException e) {
            log.error("Error reading response body", e);
        }

        log.error("Ollama API error - Method: {}, Status: {}, Message: {}", 
                methodKey, response.status(), errorMessage);

        return switch (response.status()) {
            case 400 -> new OllamaApiException("Bad Request: " + errorMessage, response.status());
            case 401 -> new OllamaApiException("Unauthorized: " + errorMessage, response.status());
            case 403 -> new OllamaApiException("Forbidden: " + errorMessage, response.status());
            case 404 -> new OllamaApiException("Not Found: " + errorMessage, response.status());
            case 429 -> new OllamaApiException("Rate Limited: " + errorMessage, response.status());
            case 500 -> new OllamaApiException("Internal Server Error: " + errorMessage, response.status());
            case 503 -> new OllamaApiException("Service Unavailable: " + errorMessage, response.status());
            default -> new OllamaApiException("HTTP " + response.status() + ": " + errorMessage, response.status());
        };
    }
}
