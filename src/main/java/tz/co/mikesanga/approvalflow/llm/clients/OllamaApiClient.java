package tz.co.mikesanga.approvalflow.llm.clients;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import tz.co.mikesanga.approvalflow.llm.dtos.OllamaChatRequest;
import tz.co.mikesanga.approvalflow.llm.dtos.OllamaChatResponse;

/**
 * Feign client for Ollama API integration
 */
@FeignClient(
        name = "ollama-api",
        url = "${ollama.api.base-url}",
        configuration = tz.co.mikesanga.approvalflow.llm.config.FeignConfig.class
)
public interface OllamaApiClient {

    /**
     * Generate a chat completion using Ollama API
     *
     * @param request the chat completion request
     * @return the chat completion response
     */
    @PostMapping("/api/chat")
    OllamaChatResponse chatCompletion(@RequestBody OllamaChatRequest request);

    /**
     * Generate a completion using Ollama API (alternative endpoint)
     *
     * @param request the completion request
     * @return the completion response
     */
    @PostMapping("/api/generate")
    OllamaChatResponse generate(@RequestBody OllamaChatRequest request);
}
