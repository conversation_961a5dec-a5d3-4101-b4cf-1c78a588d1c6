package tz.co.mikesanga.approvalflow.llm.dtos;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for LLM chat request from GraphQL API
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class LlmChatRequest {

    /**
     * The message to send to the LLM
     */
    @NotBlank(message = "Message is required")
    private String message;

    /**
     * Optional model to use (if not specified, default model will be used)
     */
    private String model;

    /**
     * Optional temperature for response generation (0.0 to 1.0)
     */
    private Double temperature;

    /**
     * Optional maximum number of tokens to generate
     */
    private Integer maxTokens;
}
